import { ref, reactive, computed, h } from 'vue'
import messages from '@/utils/messages'
import usedVehicleInventoryApi from '@/api/usedVehicleInventory'
import { getDictOptions } from '@/api/dict'
import { useDictOptions, getDictLabel } from '@/utils/dictUtils'
import { NIcon, NTag, NButton, useDialog } from 'naive-ui'
import {
    SearchOutline,
    RefreshOutline,
    AddOutline,
    CreateOutline,
    ContractOutline,
    ExpandOutline,
    SwapHorizontalOutline,
    CopyOutline,
    LogOutOutline,
    PersonOutline,
    CloseOutline
} from '@vicons/ionicons5'
import SearchIcon from '@/components/icons/SearchIcon.vue'
import { Building } from '@vicons/tabler'
import {
    dateRangeOptions,
    getDateRangeParams,
    handleDateRangeChange as handleDateChange,
    handleCustomDateChange as handleCustomDate
} from '@/utils/dateRange'
import FileUploadButton from '@/components/FileUploadButton.vue'
import BizOrgSelector from '@/components/bizOrg/BizOrgSelector.vue'
import OutboundDialog from '@/components/inventory/OutboundDialog.vue'
import CustomerSelector from '@/components/customer/CustomerSelector.vue'
import { getNumberInputConfig } from '@/config/inputConfig'

export default function useUsedVehicleInventory() {
    // 初始化对话框
    const dialog = useDialog()
    window.$dialog = dialog

    // 状态变量
    const tableRef = ref(null)
    const formRef = ref(null)
    const loading = ref(false)
    const dialogVisible = ref(false)
    const dialogTitle = ref('新增二手车库存')
    const isEdit = ref(false)
    const isEditDialogMaximized = ref(true)
    const showOrgSelector = ref(false)
    const outboundDialogVisible = ref(false)
    const customerSelectorVisible = ref(false)
    const windowHeight = ref(window.innerHeight)

    // 计算表格最大高度
    const tableMaxHeight = computed(() => {
        const screenHeight = windowHeight.value
        const pagepadding = 32
        const filterHeight = 140
        const toolbarHeight = 60
        const paginationHeight = 50
        const margin = 20
        const calculatedHeight = screenHeight - pagepadding - filterHeight - toolbarHeight - paginationHeight - margin
        let finalHeight
        if (screenHeight >= 1440) {
            finalHeight = Math.max(calculatedHeight, 700)
        } else if (screenHeight >= 1080) {
            finalHeight = Math.max(calculatedHeight, 500)
        } else if (screenHeight >= 768) {
            finalHeight = Math.max(calculatedHeight, 400)
        } else {
            finalHeight = Math.max(calculatedHeight, 300)
        }
        return finalHeight - 185;
    })

    // 选项数据
    const stockStatusOptions = ref([{ label: '不限', value: null }])
    const vehicleSourceOptions = ref([{ label: '不限', value: null }])
    const leadsSourceOptions = ref([{ label: '不限', value: null }])

    // 使用响应式字典数据
    const { options: stockStatusDictOptions } = useDictOptions('stock_status', false)
    const { options: vehicleSourceDictOptions } = useDictOptions('vehicle_source', false)

    // 为了兼容现有代码，创建别名
    const stockStatusDict = stockStatusDictOptions
    const vehicleSourceDict = vehicleSourceDictOptions

    // 金额输入框配置
    const amountInputConfig = computed(() => {
        return getNumberInputConfig("amount");
    })

    // 筛选表单
    const filterForm = reactive({
        dateRange: null,
        customDateRange: null,
        stockStatus: null,
        vehicleSource: null,
        stockOrgs: [],
        keywords: ''
    })

    // 表单数据
    const form = reactive({
        id: null,
        vehicleId: '',
        vin: '',
        brand: '',
        series: '',
        vehicleType: '',
        color: '',
        mileage: null,
        registrationDate: null,
        insuranceDeadline: null,
        checkDeadline: null,
        inboundAmount: null,
        inboundPaymentMethod: 'cash',
        inboundDate: null,
        inboundOrgId: null,
        inboundCustomerId: null,
        customerInfo: null,
        inboundAgentId: null,
        stockOrgId: null,
        stockStatus: 'stocking',
        vehicleSource: 'VEHICLE_EXCHANGE',
        leadsSource: '线下',
        leadsProviderName: '',
        leadsProviderPhone: '',
        refOrderId: null,
        remark: ''
    })

    // 表单验证规则
    const rules = {
        vehicleId: {
            required: true,
            message: '请输入车牌号',
            trigger: ['blur', 'input'],
            validator: (_, value) => {
                if (!value) {
                    return new Error('请输入车牌号')
                }

                // 车牌号必须是7位或8位
                if (value.length !== 7 && value.length !== 8) {
                    return new Error('车牌号必须是7位或8位字符')
                }

                return true
            }
        },
        vin: {
            required: true,
            message: '请输入VIN',
            trigger: ['blur', 'input'],
            validator: (_, value) => {
                if (!value) {
                    return new Error('请输入VIN')
                }

                // VIN必须是17位字符
                if (value.length !== 17) {
                    return new Error('车架号VIN必须是17位字符')
                }

                return true
            }
        },
        brand: {
            required: true,
            message: '请输入车辆品牌',
            trigger: ['blur', 'input'],
            validator: (_, value) => {
                if (!value) {
                    return new Error('请输入车辆品牌')
                }

                // 检查长度限制
                if (value.length > 20) {
                    return new Error('车辆品牌不能超过20个字符')
                }

                return true
            }
        },
        series: {
            required: true,
            message: '请输入车型系列',
            trigger: ['blur', 'input'],
            validator: (_, value) => {
                if (!value) {
                    return new Error('请输入车型系列')
                }

                // 检查长度限制
                if (value.length > 20) {
                    return new Error('车型系列不能超过20个字符')
                }

                return true
            }
        },
        inboundAmount: {
            required: true,
            type: 'number',
            message: '请输入入库金额',
            trigger: ['blur', 'change'],
            validator: (_, value) => {
                if (value === null || value === undefined || value === '') {
                    return new Error('请输入入库金额')
                }
                if (value <= 0) {
                    return new Error('入库金额必须大于0')
                }
                return true
            }
        },
        inboundDate: {
            required: true,
            message: '请选择入库日期',
            trigger: ['blur', 'change'],
            validator: (_, value) => {
                if (value === null || value === undefined) {
                    return new Error('请选择入库日期')
                }
                return true
            }
        },
        inboundCustomerId: {
            required: true,
            message: '请选择客户',
            trigger: ['blur', 'change'],
            validator: (_, _value) => {
                if (!form.inboundCustomerId || !form.customerInfo) {
                    return new Error('请选择客户')
                }
                return true
            }
        }
    }

    // 数据列表
    const inventoryData = ref([])

    // 分页配置
    const pagination = reactive({
        page: 1,
        pageSize: 20,
        showSizePicker: true,
        pageSizes: [20, 50, 100],
        itemCount: 0,
        showQuickJumper: false
    })

    // 详情弹窗
    const detailDialogVisible = ref(false)
    const currentDetailId = ref(null)

    // 出库弹窗相关
    const currentOutboundData = ref(null)

    // 复制文本到剪贴板
    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text)
            .then(() => messages.success('已复制到剪贴板'))
            .catch(() => messages.error('复制失败'))
    }

    // 计算属性：选中机构的显示文本
    const selectedOrgText = computed(() => {
        if (filterForm.stockOrgs && filterForm.stockOrgs.length > 0) {
            if (filterForm.stockOrgs.length === 1) {
                return filterForm.stockOrgs[0].orgName
            } else {
                return `已选择 ${filterForm.stockOrgs.length} 个机构`
            }
        }
        return "选择库存单位"
    })

    // 计算属性：客户显示文本
    const customerDisplayText = computed(() => {
        if (form.customerInfo) {
            return `${form.customerInfo.customerName} (${form.customerInfo.mobile || "无手机号"
                })`
        }
        return ""
    })

    // 计算属性：过滤后的数据
    const filteredData = computed(() => {
        return inventoryData.value
    })

    // 计算属性：表格滚动宽度
    const scrollX = computed(() => {
        return 2380 // 根据列数调整，适配24寸屏幕，增加存放地点列后调整宽度
    })

    // 辅助方法定义（需要在其他方法之前定义）
    const loadInventoryData = async () => {
        loading.value = true
        try {
            // 构建查询参数
            const params = {
                page: pagination.page,
                size: pagination.pageSize
            }

            // 添加筛选条件
            if (filterForm.keywords) {
                params.keywords = filterForm.keywords
            }

            if (filterForm.stockStatus) {
                params.stockStatus = filterForm.stockStatus
            }

            if (filterForm.vehicleSource) {
                params.vehicleSource = filterForm.vehicleSource
            }

            if (filterForm.stockOrgs && filterForm.stockOrgs.length > 0) {
                params.stockOrgIds = filterForm.stockOrgs.map(org => org.id).join(',')
            }

            // 添加日期范围参数
            const dateParams = getDateRangeParams(filterForm)
            Object.assign(params, dateParams)

            // 调用API获取数据
            const response = await usedVehicleInventoryApi.getInventoryList(params)

            if (response.code === 200) {
                // 直接使用返回的数据列表
                inventoryData.value = response.data.list

                // 更新分页信息
                pagination.itemCount = response.data.total
                pagination.pageCount = response.data.pages

                // 确保当前页码与API返回的一致
                if (pagination.page !== response.data.pageNum) {
                    pagination.page = response.data.pageNum
                }

                // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
                if (pagination.page > response.data.pages && response.data.pages > 0) {
                    pagination.page = response.data.pages
                    refreshData()
                    return
                }
            } else {
                messages.error(response.message || '数据加载失败')
            }
        } catch (error) {
            console.error('加载数据失败:', error)
            messages.error('加载数据失败，请稍后重试')
        } finally {
            loading.value = false
        }
    }

    const refreshData = async () => {
        await loadInventoryData()
    }

    // 方法定义（需要在 columns 之前定义）
    const handleView = (row) => {
        currentDetailId.value = row.id
        detailDialogVisible.value = true
    }

    const handleEdit = (row) => {
        isEdit.value = true
        dialogTitle.value = '编辑库存'
        Object.assign(form, row)

        // 转换金额格式：从分转换为元
        if (form.inboundAmount) {
            form.inboundAmount = form.inboundAmount / 100
        }

        // 转换日期格式：将字符串日期转换为时间戳
        if (form.inboundDate) {
            const date = new Date(form.inboundDate)
            if (!isNaN(date.getTime())) {
                form.inboundDate = date.getTime()
            } else {
                console.warn('Invalid inboundDate:', form.inboundDate)
                form.inboundDate = null
            }
        }
        if (form.registrationDate) {
            const date = new Date(form.registrationDate)
            if (!isNaN(date.getTime())) {
                form.registrationDate = date.getTime()
            } else {
                form.registrationDate = null
            }
        }
        if (form.insuranceDeadline) {
            const date = new Date(form.insuranceDeadline)
            if (!isNaN(date.getTime())) {
                form.insuranceDeadline = date.getTime()
            } else {
                form.insuranceDeadline = null
            }
        }
        if (form.checkDeadline) {
            const date = new Date(form.checkDeadline)
            if (!isNaN(date.getTime())) {
                form.checkDeadline = date.getTime()
            } else {
                form.checkDeadline = null
            }
        }
        dialogVisible.value = true
    }

    // 出库相关方法
    const handleOutbound = (row) => {
        currentDetailId.value = row.id
        currentOutboundData.value = row
        outboundDialogVisible.value = true
    }

    const handleOutboundSuccess = () => {
        outboundDialogVisible.value = false
        refreshData()
    }

    // 表格列配置
    const columns = [
        {
            title: '车牌号',
            key: 'vehicleId',
            width: 160,
            fixed: 'left',
            align: 'center',
            ellipsis: {
                tooltip: true
            },
            render(row) {
                return h(
                    'div',
                    {
                        style: {
                            alignItems: 'center',
                            display: 'flex',
                            cursor: 'pointer',
                            fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                        },
                        onClick: () => copyToClipboard(row.vehicleId),
                        title: '点击复制车牌号'
                    },
                    [
                        h('span', {
                            style: {
                                marginRight: '4px'
                            }
                        }, row.vehicleId),
                        h(
                            NIcon,
                            {
                                size: 16,
                                color: 'var(--primary-color)',
                                style: {
                                    opacity: 0.8,
                                    transition: 'opacity 0.2s',
                                    flexShrink: 0
                                }
                            },
                            { default: () => h(CopyOutline) }
                        )
                    ]
                )
            }
        },
        {
            title: 'VIN',
            key: 'vin',
            fixed: 'left',
            width: 240,
            align: 'center',
            ellipsis: {
                tooltip: true
            },
            render(row) {
                return h(
                    'div',
                    {
                        style: {
                            alignItems: 'center',
                            display: 'flex',
                            cursor: 'pointer',
                            fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                        },
                        onClick: () => copyToClipboard(row.vin),
                        title: '点击复制VIN'
                    },
                    [
                        h('span', {
                            style: {
                                marginRight: '4px'
                            }
                        }, row.vin),
                        h(
                            NIcon,
                            {
                                size: 16,
                                color: 'var(--primary-color)',
                                style: {
                                    opacity: 0.8,
                                    transition: 'opacity 0.2s',
                                    flexShrink: 0
                                }
                            },
                            { default: () => h(CopyOutline) }
                        )
                    ]
                )
            }
        },
        {
            title: '入库日期',
            key: 'inboundDate',
            width: 130,
            align: 'center',
            render(row) {
                if (!row.inboundDate) return '-'
                const date = new Date(row.inboundDate)
                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                return `${year}-${month}-${day}`
            }
        },
        {
            title: '存放地点',
            key: 'stockingOrgName',
            width: 180,
            align: 'center',
            ellipsis: {
                tooltip: true
            }
        },
        {
            title: '品牌',
            key: 'brand',
            width: 180,
            align: 'center',
            ellipsis: {
                tooltip: true
            }
        },
        {
            title: '车型系列',
            key: 'series',
            width: 340,
            align: 'center',
            ellipsis: {
                tooltip: true
            }
        },
        {
            title: '车辆类型',
            key: 'vehicleType',
            width: 150,
            align: 'center',
            ellipsis: {
                tooltip: true
            }
        },
        {
            title: '库存状态',
            key: 'stockStatus',
            align: 'center',
            width: 130,
            render(row) {
                // 从dictData.stock_status中查找对应的状态信息
                const statusOption = stockStatusDict.value.find(option => option.value === row.stockStatus)
                const status = statusOption ? {
                    text: statusOption.label,
                    type: statusOption.type || 'default',
                    color: statusOption.color
                } : {
                    text: row.stockStatus || '未知',
                    type: 'default',
                    color: null
                }

                return h(
                    NTag,
                    {
                        type: status.type,
                        size: 'small',
                        round: true,
                        style: {
                            padding: '0 10px',
                            fontWeight: 'bold',
                            ...(status.color ? { backgroundColor: status.color, color: '#fff' } : {})
                        }
                    },
                    { default: () => status.text }
                )
            }
        },
        {
            title: '入库金额(元)',
            key: 'inboundAmount',
            width: 160,
            align: 'center',
            sorter: (a, b) => a.inboundAmount - b.inboundAmount,
            render(row) {
                if (!row.inboundAmount) return '-'
                const amountInYuan = row.inboundAmount / 100
                const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                })
                return h('span', { style: { fontWeight: 'bold' } }, `¥${formattedAmount}`)
            }
        },
        {
            title: '出库金额(元)',
            key: 'outboundAmount',
            width: 160,
            align: 'center',
            sorter: (a, b) => (a.outboundAmount || 0) - (b.outboundAmount || 0),
            render(row) {
                if (!row.outboundAmount) return '-'
                const amountInYuan = row.outboundAmount / 100
                const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                })
                return h('span', { style: { fontWeight: 'bold' } }, `¥${formattedAmount}`)
            }
        },
        {
            title: '毛利润(元)',
            key: 'grossProfit',
            width: 160,
            align: 'center',
            sorter: (a, b) => (a.grossProfit || 0) - (b.grossProfit || 0),
            render(row) {
                if (!row.grossProfit) return '-'
                const profitInYuan = row.grossProfit / 100
                const formattedProfit = profitInYuan.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                })
                const color = profitInYuan >= 0 ? '#52c41a' : '#ff4d4f'
                return h('span', {
                    style: {
                        fontWeight: 'bold',
                        color: color
                    }
                }, `¥${formattedProfit}`)
            }
        },
        {
            title: '车辆来源',
            key: 'vehicleSource',
            width: 140,
            align: 'center',
            render(row) {
                // 使用字典工具函数获取车辆来源信息
                const sourceOption = vehicleSourceDictOptions.value.find(option => option.value === row.vehicleSource)
                const source = sourceOption ? {
                    text: sourceOption.label,
                    type: sourceOption.type || 'default',
                    color: sourceOption.color
                } : {
                    text: row.vehicleSource || '未知',
                    type: 'default',
                    color: null
                }

                return h(
                    NTag,
                    {
                        type: source.type,
                        size: 'small',
                        round: true,
                        style: {
                            padding: '0 10px',
                            fontWeight: 'bold',
                            ...(source.color ? { backgroundColor: source.color, color: '#fff' } : {})
                        }
                    },
                    { default: () => source.text }
                )
            }
        },
        {
            title: '线索来源',
            key: 'leadsSource',
            width: 120,
            align: 'center',
            ellipsis: {
                tooltip: true
            }
        },
        {
            title: '操作',
            key: 'actions',
            width: 160,
            align: 'center',
            render(row) {
                return h(
                    'div',
                    {
                        style: {
                            display: 'flex',
                            gap: '8px',
                            justifyContent: 'center'
                        }
                    },
                    (() => {
                        const buttons = [
                            h(
                                NButton,
                                {
                                    size: 'small',
                                    type: 'success',
                                    ghost: true,
                                    circle: true,
                                    onClick: () => handleView(row),
                                    title: '查看详情'
                                },
                                {
                                    icon: () => h(SearchIcon, { size: 16, color: '#18a058' })
                                }
                            ),
                            h(
                                NButton,
                                {
                                    size: 'small',
                                    type: 'info',
                                    ghost: true,
                                    circle: true,
                                    onClick: () => handleEdit(row),
                                    title: '编辑'
                                },
                                {
                                    icon: () => h(NIcon, { size: 16 }, { default: () => h(CreateOutline) })
                                }
                            )
                        ]

                        // 只有库存状态为"stocking"（在库）的车辆才显示出库按钮
                        if (row.stockStatus === 'stocking') {
                            buttons.push(
                                h(
                                    NButton,
                                    {
                                        size: 'small',
                                        type: 'warning',
                                        ghost: true,
                                        circle: true,
                                        onClick: () => handleOutbound(row),
                                        title: '出库'
                                    },
                                    {
                                        icon: () => h(NIcon, { size: 16 }, { default: () => h(LogOutOutline) })
                                    }
                                )
                            )
                        }

                        return buttons
                    })()
                )
            }
        }
    ]

    // 辅助方法定义
    const handleSearch = () => {
        pagination.page = 1
        refreshData()
    }

    const resetForm = () => {
        Object.assign(form, {
            id: null,
            vehicleId: '',
            vin: '',
            brand: '',
            series: '',
            vehicleType: '',
            color: '',
            mileage: null,
            registrationDate: null,
            insuranceDeadline: null,
            checkDeadline: null,
            inboundAmount: null,
            inboundPaymentMethod: 'cash',
            inboundDate: null,
            inboundOrgId: null,
            inboundCustomerId: null,
            customerInfo: null,
            inboundAgentId: null,
            stockOrgId: null,
            stockStatus: 'stocking',
            vehicleSource: 'VEHICLE_EXCHANGE',
            leadsSource: '线下',
            leadsProviderName: '',
            leadsProviderPhone: '',
            refOrderId: null,
            remark: ''
        })
    }

    const loadDictData = async () => {
        try {
            // 加载库存状态选项 - 使用响应式字典数据
            stockStatusOptions.value = [
                { label: '不限', value: null },
                ...stockStatusDictOptions.value
            ]

            // 加载车辆来源选项 - 使用响应式字典数据
            vehicleSourceOptions.value = [
                { label: '不限', value: null },
                ...vehicleSourceDictOptions.value
            ]

            // 加载线索来源选项
            const leadsResponse = await getDictOptions('leads_source')
            if (leadsResponse.code === 200) {
                leadsSourceOptions.value = [
                    { label: '不限', value: null },
                    ...leadsResponse.data.map(item => ({
                        label: item.label,
                        value: item.value
                    }))
                ]
            }
        } catch (error) {
            console.error('加载字典数据失败:', error)
        }
    }

    // 返回所有需要的变量和方法
    return {
        // 组件引用
        FileUploadButton,
        BizOrgSelector,
        OutboundDialog,
        CustomerSelector,

        // 图标
        SearchOutline,
        RefreshOutline,
        AddOutline,
        SearchIcon,
        CreateOutline,
        ContractOutline,
        ExpandOutline,
        SwapHorizontalOutline,
        CopyOutline,
        LogOutOutline,
        PersonOutline,
        CloseOutline,
        Building,

        // 响应式数据
        tableRef,
        formRef,
        loading,
        dialogVisible,
        dialogTitle,
        isEdit,
        isEditDialogMaximized,
        stockStatusOptions,
        vehicleSourceOptions,
        leadsSourceOptions,
        stockStatusDict,
        vehicleSourceDict,
        filterForm,
        form,
        rules,
        inventoryData,
        pagination,
        windowHeight,
        detailDialogVisible,
        currentDetailId,
        showOrgSelector,
        outboundDialogVisible,
        currentOutboundData,
        customerSelectorVisible,

        // 计算属性
        tableMaxHeight,
        filteredData,
        columns,
        scrollX,
        selectedOrgText,
        customerDisplayText,
        amountInputConfig,

        // 日期相关
        dateRangeOptions,

        // 业务方法

        handleDateRangeChange: (value) => {
            handleDateChange(value, filterForm)
            handleSearch()
        },
        handleCustomDateChange: (value) => {
            handleCustomDate(value, filterForm)
            handleSearch()
        },
        handleSearch: () => {
            pagination.page = 1
            refreshData()
        },
        showAddDialog: () => {
            isEdit.value = false
            dialogTitle.value = '新增库存'
            resetForm()
            dialogVisible.value = true
        },
        handleEdit,
        handleView,
        handleSave: async () => {
            try {
                await formRef.value?.validate()

                const formData = { ...form }

                // 转换金额格式：从元转换为分
                if (formData.inboundAmount) {
                    formData.inboundAmount = Math.round(formData.inboundAmount * 100)
                }

                // 转换日期格式
                if (formData.inboundDate) {
                    formData.inboundDate = new Date(formData.inboundDate).toISOString().split('T')[0]
                }
                if (formData.registrationDate) {
                    formData.registrationDate = new Date(formData.registrationDate).toISOString().split('T')[0]
                }
                if (formData.insuranceDeadline) {
                    formData.insuranceDeadline = new Date(formData.insuranceDeadline).toISOString().split('T')[0]
                }
                if (formData.checkDeadline) {
                    formData.checkDeadline = new Date(formData.checkDeadline).toISOString().split('T')[0]
                }

                let response
                if (isEdit.value) {
                    response = await usedVehicleInventoryApi.updateInventory(formData.id, formData)
                } else {
                    response = await usedVehicleInventoryApi.createInventory(formData)
                }

                if (response.code === 200) {
                    messages.success(isEdit.value ? '更新成功' : '新增成功')
                    dialogVisible.value = false
                    refreshData()
                } else {
                    messages.error(response.message || '操作失败')
                }
            } catch (error) {
                console.error('保存失败:', error)
                if (error.message) {
                    messages.error(error.message)
                }
            }
        },
        handlePageChange: (page) => {
            pagination.page = page
            refreshData()
        },
        handlePageSizeChange: (pageSize) => {
            pagination.pageSize = pageSize
            pagination.page = 1
            refreshData()
        },
        handleImportSuccess: (result) => {
            messages.success(`导入成功，共导入 ${result.successCount} 条记录`)
            refreshData()
        },
        handleImportError: (error) => {
            messages.error(`导入失败：${error.message}`)
        },
        handleChangeStatus: async (row, status) => {
            try {
                const response = await usedVehicleInventoryApi.updateInventoryStatus(row.id, status)
                if (response.code === 200) {
                    messages.success('状态更新成功')
                    refreshData()
                } else {
                    messages.error(response.message || '状态更新失败')
                }
            } catch (error) {
                console.error('状态更新失败:', error)
                messages.error('状态更新失败，请稍后重试')
            }
        },
        refreshData,

        // 机构选择器相关方法
        handleOrgSelect: (selectedOrgs) => {
            filterForm.stockOrgs = selectedOrgs
            showOrgSelector.value = false
            handleSearch()
        },
        handleOrgCancel: () => {
            showOrgSelector.value = false
        },
        clearOrgSelection: () => {
            filterForm.stockOrgs = []
            handleSearch()
        },
        removeOrg: (orgToRemove) => {
            filterForm.stockOrgs = filterForm.stockOrgs.filter(org => org.id !== orgToRemove.id)
            handleSearch()
        },

        // 客户选择器相关方法
        showCustomerSelector: () => {
            customerSelectorVisible.value = true
        },
        handleCustomerSelect: (customer) => {
            form.inboundCustomerId = customer.id
            form.customerInfo = customer
            customerSelectorVisible.value = false
        },
        handleCustomerSelectCancel: () => {
            customerSelectorVisible.value = false
        },
        clearCustomer: () => {
            form.inboundCustomerId = null
            form.customerInfo = null
        },

        // 出库相关方法
        handleOutbound,
        handleOutboundSuccess,

        // 生命周期方法
        initialize: async () => {
            window.addEventListener('resize', () => {
                windowHeight.value = window.innerHeight
            })
            await loadDictData()
            await refreshData()
        },
        cleanup: () => {
            window.removeEventListener('resize', () => {
                windowHeight.value = window.innerHeight
            })
        }
    }
}
